import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Dimensions,
  Image,
} from 'react-native';
import { useLocalSearchParams } from 'expo-router';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  withSpring,
  withDelay,
  interpolate,
  Easing,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { MagicLinkService } from '@/services/magicLinkService';
import { useDeepLinking } from '@/lib/hooks/useDeepLinking';
import { DesignSystem } from '@/constants/DesignSystem';
import { useThemeColor } from '@/hooks/useThemeColor';
import { validateEmail } from '@/lib/utils/validation';
import { handleAuthError, withRetry } from '@/lib/utils/errorHandling';

const { width, height } = Dimensions.get('window');

export default function LoginScreen() {
  const { email: paramEmail } = useLocalSearchParams<{ email?: string }>();
  const [email, setEmail] = useState(paramEmail || '');
  const [emailError, setEmailError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  // Animation values
  const fadeAnim = useSharedValue(0);
  const slideAnim = useSharedValue(50);
  const logoScale = useSharedValue(0.8);
  const formOpacity = useSharedValue(0);
  const successScale = useSharedValue(0);

  // Theme colors
  const backgroundColor = useThemeColor(
    { light: DesignSystem.Colors.light.background, dark: DesignSystem.Colors.dark.background },
    'background'
  );

  // Initialize deep linking
  useDeepLinking();

  useEffect(() => {
    // Clean up expired magic link tokens on app start
    MagicLinkService.cleanupExpiredTokens();
    
    // Start entrance animations
    startEntranceAnimation();
  }, []);

  const startEntranceAnimation = () => {
    fadeAnim.value = withTiming(1, { duration: 800, easing: Easing.out(Easing.ease) });
    slideAnim.value = withSpring(0, DesignSystem.Animations.spring.gentle);
    logoScale.value = withDelay(200, withSpring(1, DesignSystem.Animations.spring.bouncy));
    formOpacity.value = withDelay(400, withTiming(1, { duration: 600 }));
  };

  const validateEmailField = (emailValue: string): boolean => {
    const result = validateEmail(emailValue);
    if (!result.isValid) {
      setEmailError(result.error || 'Invalid email');
      return false;
    }
    setEmailError('');
    return true;
  };

  const handleEmailChange = (value: string) => {
    setEmail(value);
    if (emailError) {
      // Clear error when user starts typing
      setEmailError('');
    }
  };

  const handleEmailLogin = async () => {
    if (!validateEmail(email)) {
      return;
    }

    setIsLoading(true);
    try {
      console.log('Sending magic link to:', email);

      const result = await MagicLinkService.sendMagicLink({
        email: email.trim(),
        redirectUrl: '/(tabs)',
      });

      if (result.success) {
        setShowSuccess(true);
        successScale.value = withSpring(1, DesignSystem.Animations.spring.bouncy);
        
        // Auto-hide success message after 3 seconds
        setTimeout(() => {
          setShowSuccess(false);
          successScale.value = withTiming(0, { duration: 300 });
          setEmail('');
        }, 3000);
      } else {
        Alert.alert(
          'Error',
          result.error || 'Failed to send magic link. Please try again.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Magic link error:', error);
      Alert.alert(
        'Error',
        'Something went wrong. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleLogin = () => {
    Alert.alert('Coming Soon', 'Google login will be available soon!');
  };

  const handleGitHubLogin = () => {
    Alert.alert('Coming Soon', 'GitHub login will be available soon!');
  };

  // Animated styles
  const containerStyle = useAnimatedStyle(() => ({
    opacity: fadeAnim.value,
  }));

  const headerStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: slideAnim.value }],
  }));

  const logoStyle = useAnimatedStyle(() => ({
    transform: [{ scale: logoScale.value }],
  }));

  const formStyle = useAnimatedStyle(() => ({
    opacity: formOpacity.value,
  }));

  const successStyle = useAnimatedStyle(() => ({
    transform: [{ scale: successScale.value }],
    opacity: interpolate(successScale.value, [0, 1], [0, 1]),
  }));

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <LoadingSpinner 
          variant="dots" 
          size="large" 
          text="Sending magic link..."
        />
      </View>
    );
  }

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <LinearGradient
        colors={[
          DesignSystem.Colors.light.background,
          DesignSystem.Colors.light.backgroundSecondary,
        ]}
        style={StyleSheet.absoluteFill}
      />
      
      <ScrollView 
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        <Animated.View style={[styles.content, containerStyle]}>
          {/* Header */}
          <Animated.View style={[styles.header, headerStyle]}>
            <Animated.View style={[styles.logoContainer, logoStyle]}>
              <Image
                source={require('@/assets/images/icon.png')}
                style={styles.logo}
                resizeMode="contain"
              />
            </Animated.View>
            
            <ThemedText type="title" style={styles.title}>
              Welcome Back
            </ThemedText>
            
            <ThemedText style={styles.subtitle}>
              Sign in to your PROTEC Alumni account
            </ThemedText>
          </Animated.View>

          {/* Success Message */}
          {showSuccess && (
            <Animated.View style={[styles.successContainer, successStyle]}>
              <ThemedText style={styles.successText}>
                ✅ Magic link sent! Check your email.
              </ThemedText>
            </Animated.View>
          )}

          {/* Form */}
          <Animated.View style={[styles.form, formStyle]}>
            <Input
              label="Email Address"
              value={email}
              onChangeText={handleEmailChange}
              placeholder="Enter your email"
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
              error={emailError}
              variant="outlined"
              size="lg"
            />

            <Button
              title="Send Magic Link"
              onPress={handleEmailLogin}
              disabled={isLoading || !email.trim()}
              loading={isLoading}
              variant="primary"
              size="lg"
              fullWidth
              style={styles.primaryButton}
            />

            <View style={styles.divider}>
              <View style={styles.dividerLine} />
              <ThemedText style={styles.dividerText}>or continue with</ThemedText>
              <View style={styles.dividerLine} />
            </View>

            <View style={styles.socialButtons}>
              <Button
                title="Google"
                onPress={handleGoogleLogin}
                variant="outline"
                size="lg"
                style={styles.socialButton}
              />
              
              <Button
                title="GitHub"
                onPress={handleGitHubLogin}
                variant="outline"
                size="lg"
                style={styles.socialButton}
              />
            </View>
          </Animated.View>

          {/* Footer */}
          <View style={styles.footer}>
            <ThemedText style={styles.footerText}>
              Don't have an account? Contact your alumni coordinator
            </ThemedText>
          </View>
        </Animated.View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: DesignSystem.Colors.light.background,
  },

  scrollContent: {
    flexGrow: 1,
    minHeight: height,
  },

  content: {
    flex: 1,
    paddingHorizontal: DesignSystem.Spacing['3xl'],
    paddingTop: DesignSystem.Spacing['6xl'],
    paddingBottom: DesignSystem.Spacing['4xl'],
  },

  header: {
    alignItems: 'center',
    marginBottom: DesignSystem.Spacing['5xl'],
  },

  logoContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: DesignSystem.Colors.light.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: DesignSystem.Spacing['3xl'],
    ...DesignSystem.Shadows.lg,
  },

  logo: {
    width: 60,
    height: 60,
  },

  title: {
    fontSize: 32,
    fontWeight: '800',
    textAlign: 'center',
    marginBottom: DesignSystem.Spacing.md,
    color: DesignSystem.Colors.light.text,
  },

  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    color: DesignSystem.Colors.light.textSecondary,
    lineHeight: 24,
  },

  successContainer: {
    backgroundColor: DesignSystem.Colors.light.success,
    paddingHorizontal: DesignSystem.Spacing.lg,
    paddingVertical: DesignSystem.Spacing.md,
    borderRadius: DesignSystem.BorderRadius.lg,
    marginBottom: DesignSystem.Spacing.lg,
    alignItems: 'center',
  },

  successText: {
    color: DesignSystem.Colors.light.textInverse,
    fontWeight: DesignSystem.Typography.fontWeight.medium,
    textAlign: 'center',
  },

  form: {
    width: '100%',
    marginBottom: DesignSystem.Spacing['4xl'],
  },

  primaryButton: {
    marginTop: DesignSystem.Spacing.lg,
    marginBottom: DesignSystem.Spacing['3xl'],
  },

  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: DesignSystem.Spacing['3xl'],
  },

  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: DesignSystem.Colors.light.border,
  },

  dividerText: {
    marginHorizontal: DesignSystem.Spacing.lg,
    fontSize: DesignSystem.Typography.fontSize.sm,
    color: DesignSystem.Colors.light.textMuted,
  },

  socialButtons: {
    flexDirection: 'row',
    gap: DesignSystem.Spacing.md,
  },

  socialButton: {
    flex: 1,
  },

  footer: {
    alignItems: 'center',
    marginTop: 'auto',
  },

  footerText: {
    fontSize: DesignSystem.Typography.fontSize.sm,
    color: DesignSystem.Colors.light.textMuted,
    textAlign: 'center',
    lineHeight: 20,
  },
});
