import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ActivityIndicator,
  ViewStyle,
  TextStyle,
  TouchableOpacityProps,
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { useThemeColor } from '@/hooks/useThemeColor';
import { DesignSystem } from '@/constants/DesignSystem';

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

interface ButtonProps extends Omit<TouchableOpacityProps, 'onPress'> {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
}

// Helper functions for theme colors
function getBackgroundColors(variant: string) {
  switch (variant) {
    case 'primary':
      return { light: DesignSystem.Colors.light.primary, dark: DesignSystem.Colors.dark.primary };
    case 'secondary':
      return { light: DesignSystem.Colors.light.backgroundSecondary, dark: DesignSystem.Colors.dark.backgroundSecondary };
    case 'outline':
    case 'ghost':
      return { light: 'transparent', dark: 'transparent' };
    case 'destructive':
      return { light: DesignSystem.Colors.light.error, dark: DesignSystem.Colors.dark.error };
    default:
      return { light: DesignSystem.Colors.light.primary, dark: DesignSystem.Colors.dark.primary };
  }
}

function getTextColors(variant: string) {
  switch (variant) {
    case 'primary':
    case 'destructive':
      return { light: DesignSystem.Colors.light.textInverse, dark: DesignSystem.Colors.dark.textInverse };
    case 'secondary':
      return { light: DesignSystem.Colors.light.text, dark: DesignSystem.Colors.dark.text };
    case 'outline':
    case 'ghost':
      return { light: DesignSystem.Colors.light.primary, dark: DesignSystem.Colors.dark.primary };
    default:
      return { light: DesignSystem.Colors.light.textInverse, dark: DesignSystem.Colors.dark.textInverse };
  }
}

function getBorderColors(variant: string) {
  switch (variant) {
    case 'outline':
      return { light: DesignSystem.Colors.light.primary, dark: DesignSystem.Colors.dark.primary };
    case 'secondary':
      return { light: DesignSystem.Colors.light.border, dark: DesignSystem.Colors.dark.border };
    default:
      return { light: 'transparent', dark: 'transparent' };
  }
}

export function Button({
  title,
  onPress,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  leftIcon,
  rightIcon,
  fullWidth = false,
  style,
  onPressIn,
  onPressOut,
  ...props
}: ButtonProps) {
  const isDisabled = disabled || loading;

  // Animation values
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);

  // Theme colors
  const backgroundColor = useThemeColor(
    getBackgroundColors(variant),
    'background'
  );
  const textColor = useThemeColor(
    getTextColors(variant),
    'text'
  );
  const borderColor = useThemeColor(
    getBorderColors(variant),
    'border'
  );

  // Animated styles
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  const handlePressIn = (event: any) => {
    if (!isDisabled) {
      scale.value = withSpring(0.95, DesignSystem.Animations.spring.snappy);
      opacity.value = withTiming(0.8, { duration: DesignSystem.Animations.duration.fast });
    }
    onPressIn?.(event);
  };

  const handlePressOut = (event: any) => {
    if (!isDisabled) {
      scale.value = withSpring(1, DesignSystem.Animations.spring.gentle);
      opacity.value = withTiming(1, { duration: DesignSystem.Animations.duration.fast });
    }
    onPressOut?.(event);
  };

  const buttonStyle: ViewStyle[] = [
    styles.base,
    styles[variant],
    styles[size],
    {
      backgroundColor,
      borderColor,
    },
    fullWidth && styles.fullWidth,
    isDisabled && styles.disabled,
    style,
  ];

  const textStyle: TextStyle[] = [
    styles.text,
    styles[`${size}Text`],
    {
      color: textColor,
    },
  ];

  const getLoaderColor = () => {
    switch (variant) {
      case 'primary':
      case 'destructive':
        return DesignSystem.Colors.light.textInverse;
      default:
        return textColor;
    }
  };

  return (
    <AnimatedTouchableOpacity
      style={[buttonStyle, animatedStyle]}
      onPress={onPress}
      disabled={isDisabled}
      activeOpacity={1}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      {...props}
    >
      {loading && (
        <ActivityIndicator
          size="small"
          color={getLoaderColor()}
          style={styles.loader}
        />
      )}
      {!loading && leftIcon && <>{leftIcon}</>}
      <Text style={textStyle}>{title}</Text>
      {!loading && rightIcon && <>{rightIcon}</>}
    </AnimatedTouchableOpacity>
  );
}

const styles = StyleSheet.create({
  base: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: DesignSystem.Components.button.borderRadius,
    gap: DesignSystem.Spacing.sm,
    borderWidth: 0,
  },

  // Variants
  primary: {
    // Colors handled by theme
  },
  secondary: {
    borderWidth: 1,
  },
  outline: {
    borderWidth: 1,
  },
  ghost: {
    // Transparent background
  },
  destructive: {
    // Colors handled by theme
  },

  // Sizes
  sm: {
    paddingHorizontal: DesignSystem.Components.button.padding.sm.horizontal,
    paddingVertical: DesignSystem.Components.button.padding.sm.vertical,
    minHeight: DesignSystem.Components.button.height.sm,
  },
  md: {
    paddingHorizontal: DesignSystem.Components.button.padding.md.horizontal,
    paddingVertical: DesignSystem.Components.button.padding.md.vertical,
    minHeight: DesignSystem.Components.button.height.md,
  },
  lg: {
    paddingHorizontal: DesignSystem.Components.button.padding.lg.horizontal,
    paddingVertical: DesignSystem.Components.button.padding.lg.vertical,
    minHeight: DesignSystem.Components.button.height.lg,
  },

  // Layout
  fullWidth: {
    width: '100%',
  },

  // States
  disabled: {
    opacity: 0.5,
  },

  // Text styles
  text: {
    fontWeight: DesignSystem.Typography.fontWeight.semibold,
    textAlign: 'center',
  },

  smText: {
    fontSize: DesignSystem.Components.button.fontSize.sm,
  },
  mdText: {
    fontSize: DesignSystem.Components.button.fontSize.md,
  },
  lgText: {
    fontSize: DesignSystem.Components.button.fontSize.lg,
  },

  // Loading indicator
  loader: {
    marginRight: DesignSystem.Spacing.sm,
  },
});
